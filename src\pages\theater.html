<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theater Management - Rainbow Station Inc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #ffffff;
            color: #1f2937;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: linear-gradient(to right, #1e3a8a, #3730a3, #1e3a8a);
            color: #ffffff;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .header-content {
            width: 100%;
            max-width: none;
            padding: 16px;
        }

        .header-flex {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: space-between;
            gap: 16px;
        }

        @media (min-width: 640px) {
            .header-flex {
                flex-direction: row;
                align-items: center;
            }
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .back-btn {
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .header-title h1 {
            font-size: 20px;
            font-weight: 900;
            color: #ffffff;
            letter-spacing: 0.05em;
            margin-bottom: 4px;
        }

        @media (min-width: 640px) {
            .header-title h1 {
                font-size: 24px;
            }
        }

        @media (min-width: 1024px) {
            .header-title h1 {
                font-size: 32px;
            }
        }

        .header-title p {
            font-size: 14px;
            color: #bfdbfe;
            font-weight: 500;
        }

        @media (min-width: 640px) {
            .header-title p {
                font-size: 16px;
            }
        }

        .header-right {
            text-align: right;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 16px;
            flex-shrink: 0;
        }

        .header-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 2px;
        }

        .header-info div {
            font-size: 12px;
            font-weight: bold;
            color: #bfdbfe;
            white-space: nowrap;
        }

        .header-info span {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 2px 8px;
            font-size: 14px;
            font-weight: 900;
            color: #ffffff;
            border-radius: 4px;
            margin-left: 6px;
        }

        .header-controls {
            display: flex;
            gap: 4px;
            align-items: center;
            flex-shrink: 0;
        }

        .header-control-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
            min-width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-control-btn.refresh {
            background: rgba(6, 182, 212, 0.2);
            border-color: rgba(6, 182, 212, 0.5);
            color: #06b6d4;
        }

        .header-control-btn.refresh:hover {
            background: rgba(6, 182, 212, 0.3);
            color: #0891b2;
        }

        .header-control-btn.minimize {
            background: rgba(245, 158, 11, 0.2);
            border-color: rgba(245, 158, 11, 0.5);
        }

        .header-control-btn.close {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.5);
        }

        .header-control-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.2);
        }

        /* Main Content */
        .main-content {
            margin-top: 120px;
            padding: 32px 16px;
        }

        /* Tab Navigation */
        .tab-navigation {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 32px;
            justify-content: center;
        }

        @media (min-width: 768px) {
            .tab-navigation {
                justify-content: flex-start;
            }
        }

        .tab-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 16px 24px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background-color: #ffffff;
            color: #374151;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .tab-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: #ffffff;
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.4);
        }

        .tab-btn.exit {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: #ffffff;
            border-color: #ef4444;
        }

        .tab-btn.exit:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            border-color: #dc2626;
        }

        .tab-btn.refresh {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: #ffffff;
            border-color: #06b6d4;
        }

        .tab-btn.refresh:hover {
            background: linear-gradient(135deg, #0891b2, #0e7490);
            border-color: #0891b2;
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(6, 182, 212, 0.4);
        }

        .tab-btn.refresh.loading {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            border-color: #6b7280;
            cursor: not-allowed;
            animation: pulse 2s infinite;
        }

        .tab-btn.refresh.success {
            background: linear-gradient(135deg, #10b981, #059669);
            border-color: #10b981;
        }

        .tab-btn.refresh.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border-color: #ef4444;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        .tab-icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }

        /* Content Area */
        .content-area {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            min-height: 400px;
        }

        .content-title {
            font-size: 28px;
            font-weight: 900;
            color: #1f2937;
            margin-bottom: 16px;
            text-align: center;
        }

        .content-description {
            font-size: 18px;
            color: #6b7280;
            text-align: center;
            margin-bottom: 32px;
        }

        .placeholder-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 64px 32px;
            text-align: center;
        }

        .placeholder-icon {
            width: 80px;
            height: 80px;
            color: #d1d5db;
            margin-bottom: 24px;
        }

        .placeholder-text {
            font-size: 20px;
            color: #9ca3af;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .header-flex {
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
            }

            .header-right {
                width: 100%;
                justify-content: space-between;
            }

            .header-info div {
                font-size: 11px;
            }

            .header-info span {
                font-size: 12px;
                padding: 1px 6px;
            }

            .header-control-btn {
                padding: 3px 6px;
                font-size: 11px;
                min-width: 24px;
                height: 24px;
            }
        }

        @media (max-width: 640px) {
            .main-content {
                margin-top: 140px;
                padding: 16px;
            }

            .content-area {
                padding: 24px 16px;
            }

            .tab-btn {
                padding: 12px 16px;
                font-size: 14px;
            }

            .content-title {
                font-size: 24px;
            }

            .content-description {
                font-size: 16px;
            }

            .header-right {
                flex-direction: column;
                gap: 8px;
                align-items: flex-end;
            }

            .header-controls {
                gap: 6px;
            }

            .header-control-btn {
                padding: 4px 6px;
                font-size: 10px;
                min-width: 26px;
                height: 26px;
            }
        }

        /* Ticket Issue Form Styles */
        .ticket-issue-container {
            background-color: #ffffff;
            min-height: calc(100vh - 200px);
            padding: 40px;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        .ticket-form-wrapper {
            background-color: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            padding: 48px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            width: 100%;
            max-width: 600px;
        }

        .form-section {
            margin-bottom: 40px;
        }

        .form-label {
            display: block;
            font-size: 24px;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .duration-input {
            width: 100%;
            max-width: 300px;
            padding: 16px 20px;
            font-size: 20px;
            font-weight: 600;
            border: 3px solid #10b981;
            border-radius: 8px;
            background-color: #f9fafb;
            color: #1f2937;
            outline: none;
            transition: all 0.3s ease;
            cursor: not-allowed;
        }

        .duration-input:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
        }

        .duration-input[readonly] {
            background-color: #f3f4f6;
            color: #374151;
            border-color: #d1d5db;
        }

        .duration-unit {
            display: inline-block;
            margin-left: 12px;
            font-size: 18px;
            font-weight: 600;
            color: #6b7280;
        }

        .photo-container {
            width: 100%;
        }

        .photo-preview {
            width: 100%;
            height: 300px;
            border: 3px solid #10b981;
            border-radius: 12px;
            background-color: #f9fafb;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            overflow: hidden;
            position: relative;
        }

        .photo-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
        }

        .camera-icon {
            width: 64px;
            height: 64px;
            margin-bottom: 12px;
            color: #d1d5db;
        }

        .photo-text {
            font-size: 16px;
            font-weight: 500;
            color: #6b7280;
        }

        .captured-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .photo-controls {
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        .photo-btn {
            flex: 1;
            max-width: 150px;
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 700;
            border: 3px solid #10b981;
            border-radius: 8px;
            background-color: transparent;
            color: #10b981;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .photo-btn:hover {
            background-color: #10b981;
            color: #ffffff;
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.3);
        }

        .btn-icon {
            width: 20px;
            height: 20px;
        }

        .payment-methods {
            display: flex;
            flex-direction: column;
            gap: 15px;
            justify-content: center;
        }

        .payment-btn {
            flex: 1;
            min-width: 140px;
            max-width: 180px;
            padding: 24px 20px;
            font-size: 18px;
            font-weight: 700;
            border: 3px solid #10b981;
            border-radius: 8px;
            background-color: transparent;
            color: #10b981;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .payment-btn:hover,
        .payment-btn.selected {
            background-color: #10b981;
            color: #ffffff;
        }

        .payment-icon {
            width: 40px;
            height: 40px;
        }

        .issue-ticket-btn {
            width: 100%;
            padding: 24px 32px;
            font-size: 20px;
            font-weight: 700;
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.4);
        }

        .issue-ticket-btn:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-3px);
            box-shadow: 0 15px 25px -5px rgba(16, 185, 129, 0.5);
        }

        .issue-ticket-btn:active {
            transform: translateY(-1px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .ticket-issue-container {
                padding: 20px;
            }

            .ticket-form-wrapper {
                padding: 32px 24px;
            }

            .form-label {
                font-size: 20px;
            }

            .duration-input {
                font-size: 18px;
                padding: 14px 16px;
            }

            .photo-preview {
                height: 250px;
            }

            .photo-controls {
                flex-direction: column;
                gap: 12px;
            }

            .photo-btn {
                max-width: none;
            }

            .payment-methods {
                flex-direction: column;
                gap: 15px;
            }

            .payment-btn {
                max-width: none;
                min-width: auto;
            }

            .issue-ticket-btn {
                font-size: 18px;
                padding: 20px 24px;
            }
        }

        @media (max-width: 480px) {
            .ticket-form-wrapper {
                padding: 24px 16px;
            }

            .form-label {
                font-size: 18px;
            }

            .duration-input {
                font-size: 16px;
                padding: 12px 14px;
            }

            .photo-preview {
                height: 200px;
            }

            .camera-icon {
                width: 48px;
                height: 48px;
            }

            .photo-text {
                font-size: 14px;
            }

            .payment-icon {
                width: 24px;
                height: 24px;
            }

            .issue-ticket-btn {
                font-size: 16px;
                padding: 18px 20px;
            }
        }

        /* Ticket Success Modal Styles */
        .ticket-success-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            justify-content: center;
            align-items: center;
            animation: fadeIn 0.3s ease-in-out;
        }

        .ticket-success-modal-content {
            background: linear-gradient(135deg, #ffffff, #f9fafb);
            border: 4px solid #10b981;
            border-radius: 20px;
            padding: 40px;
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            animation: slideIn 0.3s ease-out;
        }

        .success-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #e5e7eb;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: #10b981;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            animation: bounceIn 0.6s ease-out;
        }

        .success-icon svg {
            width: 50px;
            height: 50px;
        }

        .success-main-title {
            font-size: 42px;
            font-weight: bold;
            color: #10b981;
            margin: 0 0 10px 0;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 2px 2px 4px rgba(16, 185, 129, 0.2);
            animation: slideInDown 0.5s ease-out;
        }

        .success-subtitle {
            font-size: 18px;
            color: #374151;
            margin: 0;
            font-weight: 500;
        }

        .ticket-details-container {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .ticket-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
            padding: 15px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }

        .detail-label {
            font-size: 14px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            word-break: break-all;
        }

        .success-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .success-close-btn, .success-new-ticket-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: bold;
            border: 3px solid;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .success-close-btn {
            border-color: #6b7280;
            background: #ffffff;
            color: #374151;
        }

        .success-close-btn:hover {
            border-color: #374151;
            background: #f9fafb;
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.3);
        }

        .success-new-ticket-btn {
            border-color: #10b981;
            background: #10b981;
            color: #ffffff;
        }

        .success-new-ticket-btn:hover {
            background: #059669;
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
        }

        .btn-icon {
            width: 20px;
            height: 20px;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Camera Capture Modal Styles */
        .camera-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .camera-modal-content {
            background-color: #000000;
            border: 2px solid #10b981;
            border-radius: 12px;
            padding: 24px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .camera-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .camera-modal-title {
            color: #10b981;
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }

        .webcam-source-section {
            margin-bottom: 20px;
        }

        .webcam-source-label {
            color: #10b981;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
            display: block;
        }

        .webcam-source-select {
            width: 100%;
            max-width: 300px;
            padding: 8px 12px;
            background-color: #ffffff;
            border: 2px solid #10b981;
            border-radius: 4px;
            font-size: 14px;
            color: #000000;
        }

        .camera-preview-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .camera-preview-box {
            flex: 1;
            min-width: 300px;
            aspect-ratio: 4/3;
            border: 2px solid #10b981;
            border-radius: 8px;
            background-color: #1a1a1a;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        .camera-preview-box video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .camera-preview-box canvas {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .camera-preview-placeholder {
            color: #6b7280;
            font-size: 16px;
            text-align: center;
        }

        .camera-controls {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .camera-control-btn {
            padding: 12px 24px;
            font-size: 16px;
            font-weight: bold;
            border: 2px solid #10b981;
            border-radius: 8px;
            background-color: transparent;
            color: #10b981;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            min-width: 150px;
        }

        .camera-control-btn:hover {
            background-color: #10b981;
            color: #000000;
        }

        .camera-control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .camera-control-btn.close-btn {
            background-color: #dc2626;
            border-color: #dc2626;
            color: #ffffff;
        }

        .camera-control-btn.close-btn:hover {
            background-color: #b91c1c;
            border-color: #b91c1c;
        }

        /* Responsive Design for Camera Modal */
        @media (max-width: 768px) {
            .camera-modal-content {
                width: 95%;
                padding: 16px;
            }

            .camera-preview-container {
                flex-direction: column;
            }

            .camera-preview-box {
                min-width: auto;
            }

            .camera-controls {
                flex-direction: column;
            }

            .camera-control-btn {
                min-width: auto;
            }
        }

        /* Full Width Ticket Interface Styles */
        .ticket-issue-fullwidth-container {
            display: flex;
            gap: 20px;
            height: calc(100vh - 160px);
            min-height: 550px;
            padding: 10px;
            margin: 0;
            background: #ffffff;
        }

        .ticket-controls-panel {
            flex: 1;
            background: #ffffff;
            border: 2px solid #10b981;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.15);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            max-height: 100%;
        }

        .controls-content {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 5px;
        }

        .controls-content::-webkit-scrollbar {
            width: 4px;
        }

        .controls-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .controls-content::-webkit-scrollbar-thumb {
            background: #10b981;
            border-radius: 2px;
        }

        .controls-content::-webkit-scrollbar-thumb:hover {
            background: #059669;
        }

        .ticket-preview-panel {
            flex: 1;
            background: #ffffff;
            border: 2px solid #10b981;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.15);
            display: flex;
            flex-direction: column;
            max-height: 100%;
        }

        .panel-header {
            margin-bottom: 12px;
            text-align: center;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
            flex-shrink: 0;
        }

        .panel-title {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #10b981;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 4px 0;
        }

        .title-icon {
            width: 20px;
            height: 20px;
        }

        .panel-subtitle {
            color: #374151;
            font-size: 11px;
            margin: 0;
            font-weight: 500;
        }

        .big-form-section {
            margin-bottom: 12px;
            padding: 10px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            flex-shrink: 0;
        }

        .big-form-label {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #10b981;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .label-icon {
            width: 16px;
            height: 16px;
        }

        .duration-input-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 6px;
        }

        .big-duration-input {
            flex: 1;
            max-width: 120px;
            padding: 8px 12px;
            font-size: 16px;
            font-weight: bold;
            border: 2px solid #10b981;
            border-radius: 6px;
            background-color: #ffffff;
            color: #374151;
            outline: none;
            cursor: not-allowed;
            box-shadow: 0 2px 4px -1px rgba(16, 185, 129, 0.1);
        }

        .duration-unit-big {
            color: #10b981;
            font-size: 12px;
            font-weight: bold;
        }

        .input-help-text {
            color: #6b7280;
            font-size: 10px;
            margin: 0;
            font-weight: 500;
            line-height: 1.3;
        }

        .big-payment-methods {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
            margin-bottom: 6px;
        }

        .big-payment-btn {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px 24px;
            font-size: 18px;
            font-weight: bold;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            background: #ffffff;
            color: #374151;
            cursor: pointer;
            text-transform: uppercase;
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
            width: 100%;
            margin-bottom: 12px;
        }

        .big-payment-btn:hover {
            border-color: #10b981;
            background: #dcfce7;
            color: #166534;
        }

        .big-payment-btn.selected {
            border-color: #10b981;
            background: #dcfce7;
            color: #166534;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
        }

        .big-payment-icon {
            width: 24px;
            height: 24px;
            color: #10b981;
        }

        .payment-text {
            flex: 1;
            text-align: left;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 10px;
        }

        .main-action-buttons {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 8px;
            margin-top: 0px;
        }

        .big-capture-btn, .big-clear-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 8px 10px;
            font-size: 11px;
            font-weight: bold;
            border: 2px solid;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }

        .big-capture-btn {
            border-color: #3b82f6;
            background: #3b82f6;
            color: #ffffff;
        }

        .big-capture-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -3px rgba(59, 130, 246, 0.4);
        }

        .big-clear-btn {
            border-color: #ef4444;
            background: #ef4444;
            color: #ffffff;
        }

        .big-clear-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -3px rgba(239, 68, 68, 0.4);
        }

        .big-issue-ticket-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: bold;
            border: 2px solid #10b981;
            border-radius: 8px;
            background: #10b981;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .big-issue-ticket-btn:hover {
            background: #059669;
            transform: translateY(-1px);
            box-shadow: 0 6px 10px -3px rgba(16, 185, 129, 0.5);
        }

        .big-reset-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 12px 10px;
            font-size: 11px;
            font-weight: bold;
            border: 2px solid #6b7280;
            border-radius: 8px;
            background: #ffffff;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }

        .big-reset-btn:hover {
            border-color: #374151;
            background: #f9fafb;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -3px rgba(107, 114, 128, 0.3);
        }

        .btn-icon-big {
            width: 14px;
            height: 14px;
        }

        .big-photo-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 0;
        }

        .big-photo-preview {
            width: 100%;
            height: 100%;
            max-height: 350px;
            min-height: 200px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            background: #f9fafb;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
            box-shadow: 0 4px 6px -3px rgba(0, 0, 0, 0.1);
        }

        .big-photo-placeholder {
            text-align: center;
            color: #374151;
            padding: 15px;
        }

        .photo-placeholder-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .big-camera-icon {
            width: 50px;
            height: 50px;
            color: #10b981;
            opacity: 0.8;
        }

        .big-photo-text {
            font-size: 16px;
            font-weight: bold;
            margin: 0;
            color: #374151;
        }

        .big-photo-subtext {
            font-size: 11px;
            margin: 0;
            color: #6b7280;
            line-height: 1.4;
            max-width: 220px;
        }

        .photo-help-tips {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-top: 10px;
        }

        .photo-tip {
            font-size: 10px;
            margin: 0;
            color: #10b981;
            font-weight: 600;
        }

        .big-captured-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
        }

        /* Responsive Design for Full Width Layout */
        @media (max-width: 1400px) {
            .ticket-issue-fullwidth-container {
                gap: 25px;
            }
        }

        @media (max-width: 1200px) {
            .ticket-issue-fullwidth-container {
                flex-direction: column;
                height: auto;
                gap: 20px;
                padding: 10px;
            }

            .ticket-controls-panel,
            .ticket-preview-panel {
                flex: none;
                padding: 20px;
            }

            .big-photo-container {
                min-height: 250px;
            }

            .main-action-buttons {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .big-form-section {
                margin-bottom: 15px;
                padding: 12px;
            }
        }

        @media (max-width: 768px) {
            .ticket-issue-fullwidth-container {
                padding: 5px;
                gap: 10px;
                height: calc(100vh - 140px);
            }

            .ticket-controls-panel,
            .ticket-preview-panel {
                padding: 10px;
            }

            .panel-header {
                margin-bottom: 8px;
                padding-bottom: 6px;
            }

            .panel-title {
                font-size: 16px;
            }

            .panel-subtitle {
                font-size: 10px;
            }

            .big-form-section {
                padding: 8px;
                margin-bottom: 10px;
            }

            .big-form-label {
                font-size: 12px;
                margin-bottom: 6px;
            }

            .big-duration-input {
                font-size: 14px;
                max-width: 100px;
                padding: 6px 8px;
            }

            .big-payment-btn {
                padding: 8px 10px;
                font-size: 11px;
            }

            .action-buttons {
                grid-template-columns: 1fr;
                gap: 6px;
            }

            .big-capture-btn, .big-clear-btn {
                padding: 6px 8px;
                font-size: 10px;
            }

            .big-issue-ticket-btn {
                font-size: 12px;
                padding: 10px 12px;
            }

            .big-reset-btn {
                font-size: 10px;
                padding: 10px 8px;
            }

            .big-photo-container {
                min-height: 150px;
            }

            .big-photo-text {
                font-size: 14px;
            }

            .big-photo-subtext {
                font-size: 10px;
            }

            .big-camera-icon {
                width: 35px;
                height: 35px;
            }

            .input-help-text {
                font-size: 8px;
            }

            .photo-tip {
                font-size: 9px;
            }

            .btn-icon-big {
                width: 12px;
                height: 12px;
            }

            .big-payment-icon {
                width: 14px;
                height: 14px;
            }
        }

        /* Success Modal Responsive Design */
        @media (max-width: 768px) {
            .ticket-success-modal-content {
                padding: 25px;
                width: 95%;
            }

            .success-main-title {
                font-size: 28px;
                letter-spacing: 1px;
            }

            .success-subtitle {
                font-size: 14px;
            }

            .success-icon {
                width: 60px;
                height: 60px;
            }

            .success-icon svg {
                width: 35px;
                height: 35px;
            }

            .ticket-details-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .detail-item {
                padding: 12px;
            }

            .detail-label {
                font-size: 12px;
            }

            .detail-value {
                font-size: 16px;
            }

            .success-actions {
                flex-direction: column;
                gap: 10px;
            }

            .success-close-btn, .success-new-ticket-btn {
                padding: 12px 20px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .ticket-success-modal-content {
                padding: 20px;
                width: 98%;
            }

            .success-main-title {
                font-size: 22px;
                letter-spacing: 0.5px;
            }

            .success-subtitle {
                font-size: 12px;
            }

            .success-icon {
                width: 50px;
                height: 50px;
            }

            .success-icon svg {
                width: 30px;
                height: 30px;
            }

            .ticket-details-container {
                padding: 15px;
            }

            .detail-item {
                padding: 10px;
            }

            .detail-label {
                font-size: 11px;
            }

            .detail-value {
                font-size: 14px;
            }

            .success-close-btn, .success-new-ticket-btn {
                padding: 10px 15px;
                font-size: 12px;
            }

            .btn-icon {
                width: 16px;
                height: 16px;
            }
        }

        @media (max-width: 480px) {
            .ticket-issue-fullwidth-container {
                padding: 5px;
            }

            .ticket-controls-panel,
            .ticket-preview-panel {
                padding: 15px;
            }

            .big-form-section {
                padding: 15px;
            }

            .duration-input-container {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .big-duration-input {
                max-width: 100%;
            }

            .photo-help-tips {
                flex-direction: column;
                gap: 8px;
            }

            .photo-tip {
                font-size: 14px;
            }
        }

        /* Ticket List Styles */
        .ticket-list-container {
            background-color: #ffffff;
            min-height: calc(100vh - 200px);
            padding: 20px;
        }

        .ticket-list-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }

        .ticket-list-title {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            font-size: 32px;
            font-weight: 900;
            color: #10b981;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }

        .ticket-list-subtitle {
            font-size: 16px;
            color: #6b7280;
            margin: 0;
        }

        .title-icon {
            width: 36px;
            height: 36px;
            color: #10b981;
        }

        .ticket-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            gap: 20px;
        }

        .refresh-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
        }

        .refresh-btn:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-1px);
            box-shadow: 0 6px 8px -1px rgba(16, 185, 129, 0.4);
        }

        .btn-icon {
            width: 18px;
            height: 18px;
        }

        .filter-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .filter-select {
            padding: 8px 12px;
            border: 2px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #10b981;
        }

        .tickets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .ticket-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .ticket-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
        }

        .ticket-card.active {
            border: 4px solid #10b981;
            box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3), 0 10px 15px -3px rgba(16, 185, 129, 0.2);
            background: linear-gradient(135deg, #ffffff, #f0fdf4);
            animation: activeTicketGlow 2s ease-in-out infinite alternate;
        }

        @keyframes activeTicketGlow {
            0% {
                box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3), 0 10px 15px -3px rgba(16, 185, 129, 0.2);
            }
            100% {
                box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.5), 0 10px 15px -3px rgba(16, 185, 129, 0.3);
            }
        }

        .ticket-card.expired {
            border: 3px solid #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
        }

        .ticket-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .ticket-id {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
        }

        .ticket-status {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
            font-weight: 700;
        }

        .status-expired {
            background: #fee2e2;
            color: #991b1b;
        }

        .ticket-photo {
            width: 120px;
            height: 120px;
            border-radius: 12px;
            object-fit: cover;
            margin-bottom: 15px;
            border: 2px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .ticket-card.active .ticket-photo {
            border: 3px solid #10b981;
            box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
        }

        .ticket-card.active:hover {
            transform: translateY(-4px);
            box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.4), 0 15px 25px -5px rgba(16, 185, 129, 0.3);
            cursor: pointer;
        }

        .ticket-card.active:hover .ticket-photo {
            box-shadow: 0 6px 10px -2px rgba(16, 185, 129, 0.4);
        }

        /* Banned ticket styles */
        .ticket-card.banned {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 2px solid #ef4444;
            opacity: 0.8;
        }

        .ticket-card.banned .ticket-photo {
            border: 3px solid #ef4444;
            box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.3);
            filter: grayscale(50%);
        }

        .status-banned {
            background: #ef4444;
            color: white;
            font-weight: 600;
        }

        /* Refunded ticket styles */
        .ticket-card.refunded {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            opacity: 0.8;
        }

        .ticket-card.refunded .ticket-photo {
            border: 3px solid #f59e0b;
            box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3);
            filter: grayscale(30%);
        }

        .status-refunded {
            background: #f59e0b;
            color: white;
            font-weight: 600;
        }

        .ticket-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .detail-value {
            font-size: 14px;
            color: #1f2937;
            font-weight: 600;
        }

        .ticket-duration {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 10px;
        }

        .duration-label {
            font-size: 12px;
            color: #6b7280;
            margin: 0;
        }

        .duration-value {
            font-size: 16px;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
        }

        .ticket-times {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #6b7280;
            margin-top: 10px;
        }

        .loading-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #10b981;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 20px;
            color: #d1d5db;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            color: #6b7280;
        }

        /* Responsive Design for Ticket List */
        @media (max-width: 768px) {
            .ticket-list-container {
                padding: 15px;
            }

            .tickets-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .ticket-controls {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .filter-controls {
                justify-content: center;
            }

            .ticket-list-title {
                font-size: 24px;
            }

            .ticket-card {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .ticket-list-container {
                padding: 10px;
            }

            .ticket-list-title {
                font-size: 20px;
            }

            .ticket-details {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .ticket-photo {
                width: 100px;
                height: 100px;
            }
        }

        /* Ticket Management Modal Styles */
        .ticket-management-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .ticket-management-modal-content {
            background: #ffffff;
            border-radius: 12px;
            width: 500px;
            max-width: 90vw;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .modal-header {
            background: #f8fafc;
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .modal-close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #64748b;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .modal-close-btn:hover {
            background: #e2e8f0;
            color: #1e293b;
        }

        .modal-body {
            padding: 20px;
        }

        .photo-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .modal-photo {
            width: 300px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            min-height: 80px;
            box-sizing: border-box;
        }

        .form-group textarea:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .radio-group {
            display: flex;
            gap: 30px;
            margin-top: 10px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: 500;
        }

        .radio-option input[type="radio"] {
            margin-right: 8px;
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .radio-text {
            font-size: 14px;
            color: #374151;
        }

        .modal-footer {
            background: #f8fafc;
            padding: 20px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 80px;
        }

        .refund-btn {
            background: #ef4444;
            color: white;
        }

        .refund-btn:hover {
            background: #dc2626;
        }

        .save-btn {
            background: #10b981;
            color: white;
        }

        .save-btn:hover {
            background: #059669;
        }

        .close-btn {
            background: #6b7280;
            color: white;
        }

        .close-btn:hover {
            background: #4b5563;
        }

        /* Banned Tickets Styles */
        .banned-tickets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 28px;
            margin-top: 24px;
            padding: 0 12px;
            justify-items: stretch;
            align-items: start;
        }

        /* Responsive grid adjustments */
        @media (min-width: 1200px) {
            .banned-tickets-grid {
                grid-template-columns: repeat(3, 1fr);
                max-width: 1400px;
                margin: 24px auto 0;
            }
        }

        @media (min-width: 800px) and (max-width: 1199px) {
            .banned-tickets-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 799px) {
            .banned-tickets-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 0 8px;
            }
        }

        .banned-ticket-card {
            background: #ffffff;
            border: 2px solid #ef4444;
            border-radius: 16px;
            padding: 0;
            box-shadow: 0 8px 25px -5px rgba(239, 68, 68, 0.15);
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            height: auto;
            min-height: 500px;
        }

        .banned-ticket-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px -10px rgba(239, 68, 68, 0.25);
        }

        .banned-ticket-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ef4444, #dc2626);
        }

        .banned-ticket-header {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .banned-ticket-id {
            font-size: 20px;
            font-weight: 800;
            color: #991b1b;
            margin: 0;
            letter-spacing: 0.5px;
        }

        .banned-ticket-status {
            padding: 6px 16px;
            border-radius: 25px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .banned-ticket-status.refunded {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
        }

        .banned-ticket-status.not-refunded {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
        }

        .banned-ticket-body {
            padding: 24px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .banned-ticket-photo-container {
            text-align: center;
            margin-bottom: 20px;
        }

        .banned-ticket-photo {
            width: 140px;
            height: 100px;
            object-fit: cover;
            border-radius: 12px;
            border: 3px solid #ef4444;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
            filter: grayscale(20%);
        }

        .banned-ticket-photo-placeholder {
            width: 140px;
            height: 100px;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            border-radius: 12px;
            border: 3px solid #d1d5db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            margin: 0 auto;
        }

        .banned-ticket-duration {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            margin-bottom: 20px;
        }

        .banned-duration-label {
            font-size: 12px;
            font-weight: 600;
            color: #92400e;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .banned-duration-value {
            font-size: 18px;
            font-weight: 800;
            color: #92400e;
            margin: 0;
        }

        .banned-ticket-details {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .banned-detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .banned-detail-item:last-child {
            border-bottom: none;
        }

        .banned-detail-label {
            font-size: 13px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .banned-detail-value {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .banned-ticket-reason {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            border: 2px solid #fecaca;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .banned-reason-label {
            font-weight: 700;
            color: #991b1b;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
            display: block;
        }

        .banned-reason-text {
            color: #7f1d1d;
            font-size: 14px;
            margin: 0;
            line-height: 1.5;
            font-weight: 500;
        }

        .banned-ticket-times {
            display: flex;
            justify-content: space-between;
            gap: 12px;
            margin-bottom: 20px;
        }

        .banned-time-item {
            flex: 1;
            background: #f1f5f9;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }

        .banned-time-label {
            font-size: 11px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            margin-bottom: 4px;
        }

        .banned-time-value {
            font-size: 13px;
            font-weight: 700;
            color: #1e293b;
        }

        .refund-info {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border: 2px solid #bbf7d0;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .refund-info-title {
            font-size: 12px;
            font-weight: 700;
            color: #065f46;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
            text-align: center;
        }

        .banned-ticket-actions {
            text-align: center;
            padding-top: 16px;
            border-top: 2px solid #f1f5f9;
            margin-top: auto;
        }

        .refund-banned-btn {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 14px 28px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .refund-banned-btn:hover {
            background: linear-gradient(135deg, #d97706, #b45309);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
        }

        .no-tickets-message, .error-message {
            text-align: center;
            padding: 80px 40px;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border: 2px dashed #cbd5e1;
            border-radius: 20px;
            margin: 40px 20px;
        }

        .no-tickets-message svg, .error-message svg {
            margin-bottom: 24px;
            color: #94a3b8;
            opacity: 0.7;
        }

        .no-tickets-message h3, .error-message h3 {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 12px;
            color: #334155;
            letter-spacing: -0.5px;
        }

        .no-tickets-message p, .error-message p {
            font-size: 16px;
            margin-bottom: 24px;
            color: #64748b;
            font-weight: 500;
        }

        .retry-btn {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 14px 28px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .retry-btn:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
        }

        /* Loading spinner animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design for Modal */
        @media (max-width: 768px) {
            .ticket-management-modal-content {
                width: 95vw;
                margin: 10px;
            }

            .modal-photo {
                width: 100%;
                max-width: 280px;
                height: 180px;
            }

            .radio-group {
                gap: 20px;
            }

            .modal-footer {
                flex-direction: column;
            }

            .modal-btn {
                width: 100%;
            }

            .banned-tickets-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="header-flex">
                <div class="header-left">
                    <button class="back-btn" onclick="handleExit()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="19" y1="12" x2="5" y2="12"/>
                            <polyline points="12,19 5,12 12,5"/>
                        </svg>
                        Back to POS
                    </button>
                    <div class="header-title">
                        <h1>THEATER MANAGEMENT SYSTEM</h1>
                        <p id="location-name">Rainbow Station Inc.</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="header-info">
                        <div>Date: <span id="current-date">01/01/24</span></div>
                        <div>Time: <span id="current-time">00:00:00</span></div>
                        <div>Operator: <span id="current-operator">Loading...</span></div>
                    </div>
                    <div class="header-controls">
                        <button onclick="toggleFullscreen()" id="fullscreen-btn" class="header-control-btn" title="Maximize Window">⛶</button>
                        <button onclick="minimizeApp()" class="header-control-btn minimize" title="Minimize">−</button>
                        <button onclick="closeApp()" class="header-control-btn close" title="Close">×</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-btn active" onclick="setActiveTab('tickets')" id="tab-tickets">
                <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2 6a2 2 0 012-2h16a2 2 0 012 2v2H2V6zM2 10h20v8a2 2 0 01-2 2H4a2 2 0 01-2-2v-8z"/>
                </svg>
                ISSUE TICKETS
            </button>
            <button class="tab-btn" onclick="setActiveTab('current')" id="tab-current">
                <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                </svg>
                CURRENT VIEWER LIST
            </button>
            <button class="tab-btn" onclick="setActiveTab('banned')" id="tab-banned">
                <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="4.93" y1="4.93" x2="19.07" y2="19.07"/>
                </svg>
                BANNED VIEWER LIST
            </button>
            <button class="tab-btn exit" onclick="handleExit()" id="tab-exit">
                <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
                EXIT
            </button>
            <button class="tab-btn refresh" onclick="refreshTheaterSystem()" id="tab-refresh">
                <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
                REFRESH SYSTEM
            </button>
        </div>

        <!-- Content Area -->
        <div class="content-area" id="content-area">
            <!-- Content will be loaded here -->
        </div>
    </div>

    <!-- Ticket Management Modal -->
    <div id="ticket-management-modal" class="ticket-management-modal" style="display: none;">
        <div class="ticket-management-modal-content">
            <div class="modal-header">
                <h2 id="modal-ticket-title">Ban/Grant Customer Access</h2>
                <button class="modal-close-btn" onclick="closeTicketManagementModal()">&times;</button>
            </div>

            <div class="modal-body">
                <div class="photo-section">
                    <img id="modal-ticket-photo" src="" alt="Customer Photo" class="modal-photo">
                </div>

                <div class="form-section">
                    <div class="form-group">
                        <label for="ban-reason">Ban Reason:</label>
                        <textarea id="ban-reason" placeholder="Enter reason for ban/grant decision..." rows="4"></textarea>
                    </div>

                    <div class="form-group">
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="action" value="ban" id="action-ban">
                                <span class="radio-text">Ban</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="action" value="grant" id="action-grant">
                                <span class="radio-text">Grant</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="modal-btn refund-btn" onclick="handleRefund()">REFUND</button>
                <button class="modal-btn save-btn" onclick="handleSave()">SAVE</button>
                <button class="modal-btn close-btn" onclick="closeTicketManagementModal()">Close</button>
            </div>
        </div>
    </div>

    <!-- Ticket Success Modal -->
    <div id="ticket-success-modal" class="ticket-success-modal">
        <div class="ticket-success-modal-content">
            <div class="success-header">
                <div class="success-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                </div>
                <h1 class="success-main-title">NEW TICKET SOLD</h1>
                <p class="success-subtitle">Ticket has been successfully issued and saved to database</p>
            </div>

            <div class="ticket-details-container">
                <div class="ticket-details-grid">
                    <div class="detail-item">
                        <span class="detail-label">Ticket ID:</span>
                        <span class="detail-value" id="success-ticket-id">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Duration:</span>
                        <span class="detail-value" id="success-duration">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Price:</span>
                        <span class="detail-value" id="success-price" style="color: #10b981; font-weight: bold;">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Payment Method:</span>
                        <span class="detail-value" id="success-payment">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Operator:</span>
                        <span class="detail-value" id="success-operator">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Location:</span>
                        <span class="detail-value" id="success-location">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Date & Time:</span>
                        <span class="detail-value" id="success-datetime">-</span>
                    </div>
                </div>
            </div>

            <div class="success-actions">
                <button class="success-close-btn" onclick="closeSuccessModal()">
                    <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                    CLOSE
                </button>
                <button class="success-new-ticket-btn" onclick="closeSuccessModal()">
                    <svg class="btn-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    ISSUE ANOTHER TICKET
                </button>
            </div>
        </div>
    </div>

    <!-- Camera Capture Modal -->
    <div id="camera-modal" class="camera-modal">
        <div class="camera-modal-content">
            <div class="camera-modal-header">
                <h3 class="camera-modal-title">Camera Capture</h3>
            </div>

            <!-- Webcam Source Selection -->
            <div class="webcam-source-section">
                <label class="webcam-source-label">Webcam Source</label>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <select id="webcam-source-select" class="webcam-source-select">
                        <option value="">Select Camera...</option>
                    </select>
                    <button id="refresh-cameras-btn" class="camera-control-btn" onclick="refreshCameras()" style="min-width: auto; padding: 8px 16px; font-size: 14px;">
                        🔄 Refresh
                    </button>
                </div>
                <p style="color: #6b7280; font-size: 12px; margin-top: 5px; margin-bottom: 0;">
                    Connect your USB camera and click Refresh if it doesn't appear in the list
                </p>
            </div>

            <!-- Camera Status Info -->
            <div id="camera-status" style="margin-bottom: 15px; padding: 10px; background-color: #1a1a1a; border: 1px solid #374151; border-radius: 6px; display: none;">
                <p style="color: #10b981; margin: 0; font-size: 14px;">
                    <strong>Camera Status:</strong> <span id="camera-status-text">Ready</span>
                </p>
                <p style="color: #6b7280; margin: 5px 0 0 0; font-size: 12px;" id="camera-details">
                    Resolution: Not available
                </p>
            </div>

            <!-- Camera Preview Container -->
            <div class="camera-preview-container">
                <!-- Live Camera Preview -->
                <div class="camera-preview-box">
                    <video id="camera-video" autoplay muted style="display: none;"></video>
                    <div id="camera-placeholder" class="camera-preview-placeholder">
                        <p>Select a camera source to start preview</p>
                        <p style="font-size: 12px; color: #6b7280; margin-top: 10px;">
                            💡 For USB cameras: Make sure your camera is connected and not being used by another application
                        </p>
                    </div>
                </div>

                <!-- Captured Image Preview -->
                <div class="camera-preview-box">
                    <canvas id="captured-canvas" style="display: none;"></canvas>
                    <div id="capture-placeholder" class="camera-preview-placeholder">
                        <p>Captured image will appear here</p>
                        <p style="font-size: 12px; color: #6b7280; margin-top: 10px;">
                            📸 High quality images from USB cameras supported
                        </p>
                    </div>
                </div>
            </div>

            <!-- Camera Controls -->
            <div class="camera-controls">
                <button id="remove-all-btn" class="camera-control-btn" onclick="removeAllImages()" disabled>
                    Remove All Images
                </button>
                <button id="capture-image-btn" class="camera-control-btn" onclick="captureImageFromCamera()" disabled>
                    Capture Image
                </button>
                <button id="close-camera-btn" class="camera-control-btn close-btn" onclick="closeCameraModal()">
                    Close
                </button>
            </div>
        </div>
    </div>

    <script src="theater.js"></script>
</body>
</html>
